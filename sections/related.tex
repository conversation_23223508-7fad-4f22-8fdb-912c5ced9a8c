\section{Related Work}
\label{sec:related}
\paragraph{Preference optimization.}
DPO~\citep{rafailov2023dpo} simplifies RLHF by optimizing a Bradley--Terry objective; many extensions exist (e.g., offsets, filtering).
For \emph{multi-objective} alignment, MODPO~\citep{zhou2023modpo} aggregates objectives in a scalarized DPO, while Bi-Factorial PO~\citep{bfpo2024} designs a loss for safety/helpfulness trade-offs.
Constrained DPO~\citep{zheng2024cdpo} uses dual updates to meet safety constraints.
Our work differs by \emph{(i)} adding input-conditional routing and \emph{(ii)} enforcing a capability budget over a retain set during training.

\paragraph{Adapters, MoE, and routing.}
AdapterFusion~\citep{pfeiffer2020adapterfusion} composes task adapters; MoA/MoLE extend to LoRA experts with learned gating~\citep{feng2024moa,yu2024mole}.
Recently, H\textsuperscript{3}Fusion~\citep{tekin2024h3fusion} ensembles helpful/harmless/honest experts, and MidPO~\citep{midpo2025} builds helpful/safe LoRA experts with dynamic routing.
These works do not \emph{explicitly} control retain-set capability during training.
BAG couples routing with a training-time \emph{capability budget}.

\paragraph{Alignment tax.}
Instruction/RLHF post-training can degrade pretrained capabilities~\citep{lin2024mitigating,disperse2024}.
Prior mitigations include model averaging and training curricula; we instead \emph{constrain} a proxy of capability drop and optimize alignment under that budget.