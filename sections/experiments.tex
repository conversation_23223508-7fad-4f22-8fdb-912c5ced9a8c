\section{Experiments}
\label{sec:experiments}
\paragraph{Tasks and datasets.}
We follow prior work on multi-objective alignment~\citep{zhou2023modpo,zheng2024cdpo,tekin2024h3fusion}.
For preference data we use BeaverTails~\citep{beavertails2023} with separate helpfulness/harmlessness comparisons and a TruthfulQA-derived preference set~\citep{lin2022truthfulqa}.
The retain set $\mathcal{R}$ comprises MMLU~\citep{hendrycks2021mmlu}, ARC-Challenge~\citep{clark2018arc}, HellaSwag~\citep{zellers2019hellaswag}, and GSM8K~\citep{cobbe2021gsm8k}.

\paragraph{Models and training.}
We use LLaMA-class models (7--8B) with LoRA adapters (rank $r{=}8$--$16$) on attention/FFN.
Experts: $G\!\in\!\{2,3\}$ for helpful/harmless/(honest).
Gate: two-layer MLP on the [CLS] (or pooled) embedding with temperature scaling; top-1 at inference unless stated.
Optimization: DPO with $\beta\in[0.1,0.5]$, AdamW, batch-mixing of preference and retain examples at ratio 3:1.
Budget: $\tau$ sweeps (e.g., 0.0--3.0 points of average score drop); dual step $\eta_\lambda$ grid; barrier $\kappa$ schedule from 1.0 to 10.0.
We repeat each setting with 3 seeds.

\paragraph{Baselines.}
(1) \textbf{MODPO}~\citep{zhou2023modpo}: scalarized multi-objective DPO without routing. 
(2) \textbf{C-DPO}~\citep{zheng2024cdpo}: constrained DPO balancing helpfulness/harmlessness.
(3) \textbf{MoA/MoLE}~\citep{feng2024moa,yu2024mole}: mixture-of-adapters/LoRA experts with routing but no capability budget.
(4) \textbf{H\textsuperscript{3}Fusion}~\citep{tekin2024h3fusion}: alignment fusion of helpful/harmless/honest experts (we reimplement with LoRA-only experts for parameter parity).
(5) \textbf{Single-Adapter}: train one LoRA on merged objectives.
We hold compute and total trainable parameters fixed across methods.

\paragraph{Metrics.}
(1) Alignment (pairwise win rates on BeaverTails helpfulness/harmlessness; TruthfulQA truthfulness).
(2) Capability (MMLU, ARC-C, HellaSwag, GSM8K).
(3) Budget diagnostics: \emph{Budget Satisfaction Rate} (BSR; \% of runs with $\operatorname{Tax}\le\tau$) and \emph{Constraint Violation Area} (CVA; area under $(\operatorname{Tax}-\tau)_+$ across $\tau$);
(4) Routing diagnostics: gate entropy, expert utilization, and correlation between $\alpha_g(x)$ and predicted marginal tax $-c_g(x)$.

\paragraph{Key findings (to be filled with numbers).}
\begin{itemize}[leftmargin=*]
\item \textbf{Budget control:} BAG attains BSR $>\!95\%$ across budgets while baselines violate budgets for tight $\tau$.
\item \textbf{Pareto improvement:} For fixed $\tau$, BAG dominates MODPO/C-DPO and MoA/MoLE on alignment vs.\ capability plots.
\item \textbf{Mechanism evidence:} Under tighter budgets, BAG routes more mass to low-tax experts and increases gate entropy modestly, consistent with (\ref{eq:softmax_alpha}).
\end{itemize}

\paragraph{Ablations.}
(i) No budget (set $\lambda\!=\!0$ or remove barrier); (ii) No routing (uniform $\alpha$); (iii) Parameter-level mixing instead of distribution mixing; (iv) Per-token versus per-sequence gating; (v) Proxy choices for $\operatorname{TaxProxy}$; (vi) Gate-only fine-tuning versus joint gate+experts.