\section{Method: Budget-Aware Gating (BAG)}
\label{sec:method}
\paragraph{Setup.} Let $\pi_{\mathrm{ref}}$ be a reference policy and $\{\pi_g\}_{g=1}^G$ be policies obtained by attaching and training goal-specific LoRA adapters $\{\phi_g\}$ on disjoint preference datasets $\{\mathcal{D}_g\}$ (e.g., helpful, harmless, honest). We introduce a gating network $\alpha_\psi(x) \in \Delta^G$ and define the \emph{mixture-of-distributions} policy
\begin{equation}
\pi_\theta(y\!\mid\!x) \;=\; \sum_{g=1}^G \alpha_g(x)\, \pi_g(y\!\mid\!x),\qquad \sum_g \alpha_g(x) = 1,\; \alpha_g(x)\ge 0,
\label{eq:mixture_policy}
\end{equation}
where $\theta \!=\! (\{\phi_g\},\psi)$ and $\pi_g$ denotes $\pi_{\mathrm{ref}}$ augmented with adapter $\phi_g$.

\paragraph{Mixture-of-distributions DPO.}
Given preference pairs $(x, y^+, y^-)\!\sim\!\mathcal{D}$ and inverse temperature $\beta>0$, DPO minimizes
\begin{equation}
\!\!\mathcal{L}_{\mathrm{DPO}}(\theta) \!=\! -\,\mathbb{E}\Big[\log \sigma\!\big(\beta(\Delta\ell_\theta - \Delta\ell_{\mathrm{ref}})\big)\Big],\;\;
\Delta\ell_\theta \!=\! \log \pi_\theta(y^+\!\mid\!x) - \log \pi_\theta(y^-\!\mid\!x),
\label{eq:dpo_loss}
\end{equation}
where $\sigma(\cdot)$ is the logistic function and $\Delta\ell_{\mathrm{ref}}$ uses $\pi_{\mathrm{ref}}$~\citep{rafailov2023dpo}.
The mixture log-probability uses $\log\sum$ with numerical stabilization:
\begin{equation}
\log \pi_\theta(y\!\mid\!x)= \log\!\sum_{g} \alpha_g(x)\,\exp\!\big(\log \pi_g(y\!\mid\!x)\big) = \mathrm{LSE}_g\!\Big(\log \alpha_g(x) + \log \pi_g(y\!\mid\!x)\Big).
\end{equation}
We backpropagate through both experts and gate.

\paragraph{Capability budget via a differentiable TaxProxy.}
Let $\mathcal{R}$ be a \emph{retain set} of capability tasks (e.g., MMLU/ARC/HellaSwag/GSM8K).
We define a per-batch proxy $\widehat{\operatorname{Tax}}_B(\theta)$ that estimates the capability drop relative to $\pi_{\mathrm{ref}}$:
\begin{align}
\widehat{\operatorname{Tax}}_B(\theta) \;=\; \frac{1}{|B|}\sum_{(q,r)\in B\subset\mathcal{R}} \Big[s_{\mathrm{ref}}(q,r) - s_{\theta}(q,r)\Big]_+,
\label{eq:tax_proxy}
\end{align}
where $s_\theta$ is a differentiable score estimator (e.g., loglikelihood of the gold answer or a small learned proxy $f_\psi$) and $[z]_+=\max(z,0)$.
We seek $\mathbb{E}[\operatorname{TaxProxy}]\le \tau$.

\paragraph{Barrier or dual enforcement.}
We train with either: \textbf{(i) Log-barrier} objective
\begin{equation}
\mathcal{J}_{\mathrm{bar}}(\theta) \;=\; \mathcal{L}_{\mathrm{DPO}}(\theta)\;-\;\kappa\, \log\!\big(\tau - \widehat{\operatorname{Tax}}_B(\theta)\big)\;+\;\lambda_{\mathrm{ent}}\mathbb{E}\,[H(\alpha_\psi(x))],
\label{eq:barrier_obj}
\end{equation}
with a schedule $\kappa\uparrow$; or \textbf{(ii) Lagrangian} with dual ascent
\begin{equation}
\mathcal{J}_\lambda(\theta) \;=\; \mathcal{L}_{\mathrm{DPO}}(\theta)\;+\;\lambda\,\big(\widehat{\operatorname{Tax}}_B(\theta)-\tau\big)\;+\;\lambda_{\mathrm{ent}}\mathbb{E}[H(\alpha_\psi(x))],\quad \lambda\leftarrow\big[\lambda+\eta\,(\widehat{\operatorname{Tax}}_B-\tau)\big]_+.
\label{eq:lagrangian}
\end{equation}
Entropy regularization discourages route collapse.

\paragraph{Budget-optimal gating.}
For fixed experts, consider the per-input inner problem
\begin{equation}
\alpha^*(x) = \arg\min_{\alpha\in\Delta^G}\; \sum_{g}\alpha_g\,\ell_g(x)\;+\;\mu\,\sum_{g}\alpha_g\,c_g(x)\;+\;\tau_{\mathrm{ent}}\!\sum_g \alpha_g\log\alpha_g,
\label{eq:inner_alpha}
\end{equation}
where $\ell_g$ approximates the per-goal DPO loss contribution and $c_g$ estimates marginal tax (from a retain-set proxy).
This yields the closed form
\begin{equation}
\alpha^*_g(x) \;\propto\; \exp\!\left(-\frac{\ell_g(x)+\mu\,c_g(x)}{\tau_{\mathrm{ent}}}\right),
\label{eq:softmax_alpha}
\end{equation}
revealing the \emph{budget--routing} coupling: larger $\mu$ (tighter budget) upweights low-tax experts.
In practice we parameterize $\alpha_\psi$ and train end-to-end with (\ref{eq:barrier_obj}) or (\ref{eq:lagrangian}).

\paragraph{Algorithm.}
Alg.~\ref{alg:bag} summarizes BAG with the dual variant.
\begin{algorithm}[t]
\caption{BAG with dual updates}
\label{alg:bag}
\begin{algorithmic}[1]
\State \textbf{Inputs:} Reference policy $\pi_{\mathrm{ref}}$; per-goal preference sets $\{\mathcal{D}_g\}$; retain set $\mathcal{R}$; budget $\tau$; step sizes $\eta_\theta,\eta_\lambda$
\State Train goal-specific LoRA experts $\{\pi_g\}$ on $\{\mathcal{D}_g\}$ (freeze backbone)
\State Initialize gate $\alpha_\psi$, dual $\lambda\ge 0$
\For{iterations $t=1,\dots$}
  \State Sample preference minibatch $B_{\text{pref}}$ and retain minibatch $B_{\text{ret}}\subset\mathcal{R}$
  \State Compute $\mathcal{L}_{\mathrm{DPO}}(\theta)$ using mixture policy (\ref{eq:mixture_policy})--(\ref{eq:dpo_loss})
  \State Compute $\widehat{\operatorname{Tax}}_{B_{\text{ret}}}(\theta)$ by (\ref{eq:tax_proxy})
  \State $\mathcal{J}_\lambda \leftarrow \mathcal{L}_{\mathrm{DPO}} + \lambda(\widehat{\operatorname{Tax}}-\tau) + \lambda_{\mathrm{ent}}\mathbb{E}[H(\alpha_\psi)]$
  \State $\theta \leftarrow \theta - \eta_\theta \nabla_\theta \mathcal{J}_\lambda$ \quad \textit{(update experts and gate)}
  \State $\lambda \leftarrow \max\{0,\;\lambda + \eta_\lambda(\widehat{\operatorname{Tax}}-\tau)\}$ \quad \textit{(dual ascent)}
\EndFor
\end{algorithmic}
\end{algorithm}