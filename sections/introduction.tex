\section{Introduction}
\label{sec:intro}
Direct Preference Optimization (DPO)~\citep{rafailov2023dpo} has become a default foundation for aligning LLMs from pairwise preferences.
However, optimizing a single objective frequently degrades others, e.g., helpfulness versus harmlessness or truthfulness, and can reduce pretrained capabilities---an \emph{alignment tax}~\citep{lin2024mitigating} observed after RLHF/DPO and even SFT.
Recent work addresses multi-objective alignment by scalarization~\citep{zhou2023modpo, bfpo2024}, constrained optimization~\citep{zheng2024cdpo}, or ensembling/routing over specialized experts~\citep{feng2024moa,yu2024mole,tekin2024h3fusion}.

We ask: \textbf{Can we enjoy the specialization benefits of expert routing while \emph{explicitly} controlling the alignment tax?}
We propose \textbf{BAG} (\emph{Budget-Aware Gating}), a parameter-efficient framework that couples per-goal LoRA experts with an input-conditional gate and a \emph{capability budget} enforced during preference optimization.
Concretely, BAG:
\begin{enumerate}[leftmargin=*]
\item trains a small family of goal-specific LoRA adapters (e.g., helpful, harmless, honest) on disjoint preference data;
\item learns a gating network $\alpha(x) \in \Delta^G$ that mixes experts \emph{at the distribution level}, defining a mixture policy $\pi_\theta(y\mid x) = \sum_g \alpha_g(x)\,\pi_g(y\mid x)$;
\item enforces a user-specified retain-set budget $\mathbb{E}[\operatorname{TaxProxy}(\pi_\theta)] \le \tau$ by either a logarithmic barrier or a dual update.
\end{enumerate}

\paragraph{Contributions.}
(1) We formalize \emph{capability-budgeted} multi-objective preference optimization and show that an input-conditional gate emerges as the minimizer of a per-input surrogate objective combining alignment and tax.
(2) We derive a numerically stable \emph{mixture-of-distributions DPO} loss and couple it with a barrier/dual mechanism for budget control.
(3) We propose budget diagnostics---Budget Satisfaction Rate and Violation Area---and routing-tax coupling analyses.
(4) On BeaverTails helpfulness/harmlessness~\citep{beavertails2023}, TruthfulQA~\citep{lin2022truthfulqa}, and standard capability suites (MMLU~\citep{hendrycks2021mmlu}, ARC-C~\citep{clark2018arc}, HellaSwag~\citep{zellers2019hellaswag}, GSM8K~\citep{cobbe2021gsm8k}), BAG meets capability budgets while improving the alignment-capability Pareto frontier relative to strong baselines~\citep{zhou2023modpo,zheng2024cdpo,tekin2024h3fusion}.