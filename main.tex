\documentclass{article}
\usepackage{iclr2025_conference,times}
\usepackage{hyperref}
\usepackage{url}
\usepackage{amsmath,amssymb,amsfonts,mathtools}
\usepackage{amsthm} % For theorem environments
\usepackage{booktabs}
\usepackage{wrapfig}
\usepackage{multirow}
\usepackage{enumitem}
\usepackage{microtype}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{graphicx}
\usepackage{xcolor}

% Include math commands
\input{math_commands}

% Define theorem environments
\newtheorem{theorem}{Theorem}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{corollary}[theorem]{Corollary}
\theoremstyle{definition}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\theoremstyle{remark}
\newtheorem{remark}[theorem]{Remark}

\iclrfinalcopy % Remove for submission

\title{BAG: Budget-Aware Gating for Capability-Budgeted Multi-Objective Alignment}

\author{Anonymous Authors\\
Affiliations removed for double-blind review
}

\begin{document}
\maketitle

\begin{abstract}
Post-training alignment often trades off desired social objectives (e.g., safety, honesty) against a model's pretraining capabilities, incurring an \emph{alignment tax}.
We present \textbf{BAG} (\emph{Budget-Aware Gating}), a parameter-efficient alignment framework that jointly (i) routes inputs across per-goal LoRA experts via an input-conditional gate and (ii) enforces an explicit \emph{capability budget} measured on a retain-set of general-purpose tasks during preference optimization.
BAG optimizes a \emph{mixture-of-distributions} DPO objective while penalizing or constraining estimated capability loss through a differentiable tax proxy.
We show how BAG arises from a principled bilevel view in which the gate is the minimizer of a per-input composite loss balancing alignment and tax.
We derive convergence-style guarantees for a dual update that bound long-run constraint violation and introduce diagnostics (Budget Satisfaction Rate and Violation Area) that reveal a strong coupling between routing patterns and tax control.
Across helpfulness/safety/honesty alignment, BAG achieves comparable or better alignment than prior multi-objective baselines while meeting user-prescribed capability budgets, and it expands the Pareto frontier relative to MoA/MoE-style routing without budgets.
\end{abstract}

\input{sections/introduction}
\input{sections/method}
\input{sections/theory}
\input{sections/experiments}
\input{sections/related}
\input{sections/limitations}
\input{sections/conclusion}

\subsubsection*{Reproducibility Statement}
We provide full training/evaluation details in Sec.~\ref{sec:experiments}, hyperparameters in App.~\ref{app:hp}, and release code/configs.\footnote{Artifact link to be added upon camera-ready.}
All experiments were run with three seeds; we report averages and 95\% CIs.

\bibliographystyle{iclr2025_conference}
\bibliography{refs}
\clearpage
\appendix
\input{sections/appendix}
\end{document}