\documentclass{article}
\usepackage{iclr2025_conference,times}
\usepackage{hyperref}
\usepackage{url}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{amsmath,amssymb,amsthm,mathtools}
\usepackage{microtype}
\usepackage{enumitem}
\usepackage{multirow}
\usepackage{float}
\usepackage{subcaption}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{xcolor}
\usepackage{tikz}
\usetikzlibrary{positioning,arrows.meta,fit,calc}

% Uncomment for camera ready
% \iclrfinalcopy

\title{Activation-Scoped RLHF: Aligning Without Forgetting via Subspace-Constrained Updates and Representation Anchors}

\author{Anonymous authors\\
Anonymous affiliation\\
\texttt{<EMAIL>}}

\newcommand{\ours}{\textsc{AS-RLHF}}
\newcommand{\asproj}{\textsc{AS-Proj}}
\newcommand{\cka}{\textsc{CKA}}
\newcommand{\reptax}{\textsc{RepTax}}
\newcommand{\taxbench}{\textsc{TaxBench++}}
\newcommand{\kl}{\mathrm{KL}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\bbR}{\mathbb{R}}

\theoremstyle{plain}
\newtheorem{proposition}{Proposition}
\newtheorem{lemma}{Lemma}

\begin{document}
\maketitle

\begin{abstract}
Post-training with human preferences (RLHF/DPO) can regress base capabilities such as language modeling and mathematical reasoning---\emph{alignment tax}. We introduce \emph{Activation-Scoped RLHF} (\ours), a drop-in enhancement to PPO-style RLHF that preserves task-critical \emph{capability subspaces} while aligning to feedback. \ours\ has two components: (i) \asproj, which projects \emph{LoRA} gradients onto the orthogonal complement of per-layer capability subspaces estimated from unlabeled corpora; and (ii) a \emph{representation anchor} that subtracts a linear-\cka\ penalty from the reward to discourage harmful internal drift. We give a constrained-quadratic surrogate and show our left/right LoRA projections implement the per-block KKT solution under mild assumptions. We further quantify the optimization trade-off via a \emph{removed-gradient energy} diagnostic. On public prompts and reward models (TRL PPO; UltraFeedback/StackExchange/HH-RLHF; DeBERTa-v3 RM), \ours\ shifts the reward--capability Pareto frontier outward at matched KL, reducing perplexity regression and preserving GSM8K accuracy. We release code, subspace builders (GSM8K/WikiText-103), and a fairness-focused evaluation protocol (\taxbench).%
\end{abstract}

\vspace{-1mm}
\section{Introduction}
\label{sec:intro}
Large language models (LLMs) are aligned to human preferences via supervised fine-tuning (SFT) and reinforcement learning from human feedback (RLHF)~\citep{ouyang2022instruct,ziegler2019fine,stiennon2020learning,schulman2017ppo}. While alignment improves helpfulness and safety, it often incurs an \emph{alignment tax}: degradation of language modeling, reasoning, or knowledge recall. Common mitigations---stronger KL, mixing LM loss, or post-hoc model averaging---either shrink the achievable reward or require multiple runs~\citep{wortsman2022model}.

\paragraph{Goal.} Align without forgetting: preserve identified capabilities while optimizing preference reward.

\paragraph{Key idea.} Control \emph{where} learning happens. If we identify per-layer subspaces predictive of a capability, we can prevent first-order movement in those directions while allowing adaptation elsewhere; additionally, we softly anchor internal activations to a frozen reference using a representation-similarity penalty.

\paragraph{Contributions.} We propose \ours, comprising:
\begin{enumerate}[leftmargin=4mm,itemsep=1pt,topsep=1pt]
\item \textbf{Subspace-constrained updates (\asproj).} We estimate capability subspaces $U_\ell\!\in\!\bbR^{d\times r}$ from unlabeled corpora (e.g., GSM8K questions for math; WikiText-103 for LM). During PPO with LoRA, we multiply LoRA gradients by $P_\ell\!=\!I-U_\ell U_\ell^\top$ on appropriate dimensions, forbidding first-order movement in protected directions.
\item \textbf{Representation anchor.} We subtract $\lambda(1-\mathrm{CKA})$ between the policy and a frozen reference on selected layers from the scalar reward, providing a representation-space trust region complementary to policy-space KL.
\item \textbf{Theory \& diagnostics.} We formalize a constrained quadratic PPO surrogate and show our projections are the per-block KKT solution under isotropic curvature (Prop.~\ref{prop:kkt}). We prove first-order invariance of protected directions (Prop.~\ref{prop:invariance}), and derive a \emph{removed-gradient energy} diagnostic predicting reward trade-offs.
\item \textbf{Fair evaluation.} We define \taxbench: reward at matched KL and matched length bins, WikiText-103 PPL, GSM8K accuracy, \reptax{} (mean $1-\mathrm{CKA}$), and the removed-gradient norm. We include strong baselines (LM replay, higher KL, orthogonality-only LoRA, model soup) and a DPO variant (AS-DPO) for optimizer-agnosticity.
\end{enumerate}

\begin{figure}[t]
\centering
\begin{tikzpicture}[node distance=1.1cm]
\tikzstyle{box}=[draw, rounded corners, align=center, inner sep=3pt, minimum width=2.7cm, minimum height=0.8cm]
\node[box, fill=white] (prompt) {Prompt $x$};
\node[box, right=1.8cm of prompt, fill=white] (policy) {Policy $\pi_\theta$ (LoRA)};
\node[box, right=1.8cm of policy, fill=white] (rm) {Reward model $R$};
\node[box, below=1.2cm of policy, fill=white] (ref) {Frozen ref $\pi_{\theta_0}$};
\node[box, below=1.2cm of rm, fill=white] (cka) {Linear-\cka\ Penalty};
\draw[-{Latex}] (prompt) -- node[above]{sample $y$} (policy);
\draw[-{Latex}] (policy) -- node[above]{score $R(x,y)$} (rm);
\draw[-{Latex}] (policy) -- node[right]{hidden states} (cka);
\draw[-{Latex}] (ref) -- node[left]{hidden states} (cka);
\draw[-{Latex}] (cka) -- node[right]{$-\lambda(1-\mathrm{CKA})$} (rm);
\node[box, below=1.2cm of prompt, xshift=1.8cm, fill=white] (sub) {Subspaces $U_\ell$ (PCA / probes)};
\node[box, below=1.2cm of policy, xshift=-2.2cm, fill=white] (proj) {\asproj: $g\!\leftarrow\!P_\ell g$};
\draw[-{Latex}] (sub) -- node[below]{build $P_\ell=I-U_\ell U_\ell^\top$} (proj);
\draw[-{Latex}] (proj) -- node[below]{project LoRA grads} (policy);
\end{tikzpicture}
\vspace{-2mm}
\caption{\textbf{\ours\ overview.} We add (i) \emph{activation-scoped} projection of LoRA gradients onto $U_\ell^\perp$, and (ii) a \emph{representation anchor} that subtracts a linear-\cka\ penalty from rewards using hidden states of the policy and a frozen reference.}
\label{fig:overview}
\vspace{-1mm}
\end{figure}

\section{Background}
\label{sec:background}
\paragraph{RLHF with PPO.} RLHF fine-tunes a policy $\pi_\theta$ to maximize a proxy reward $R(x,y)$ while constraining divergence from a reference $\pi_{\mathrm{ref}}$ via KL~\citep{ziegler2019fine,stiennon2020learning,ouyang2022instruct,schulman2017ppo}. Given prompts $x$ and sampled responses $y\!\sim\!\pi_\theta(\cdot|x)$, PPO updates $\theta$ using a clipped objective and a learned value head.

\paragraph{LoRA.} Low-rank adapters introduce trainable matrices into linear projections, enabling memory-efficient tuning~\citep{hu2021lora}. Let $W$ be frozen and $\Delta W\!=\!BA$ be the low-rank update; gradients flow through $A$ and $B$.

\paragraph{Representation similarity via CKA.} Linear \cka\ compares centered Gram matrices of token representations~\citep{kornblith2019similarity}. We use $(1-\mathrm{CKA})$ as a penalty that is small when policy activations match the reference on selected layers.

\section{Method: Activation-Scoped RLHF}
\label{sec:method}
\subsection{Identifying capability subspaces}
Given layer index $\ell$ and a capability corpus $\mathcal{D}$ (e.g., GSM8K questions for math~\citep{cobbe2021gsm8k}; WikiText-103 for LM~\citep{merity2016wikitext}), we collect hidden states $H_\ell\!\in\!\bbR^{N\times d}$ from the frozen base model and compute the top-$r$ PCs $U_\ell\!\in\!\bbR^{d\times r}$. We optionally use \emph{probe-defined} subspaces by training linear probes and taking principal directions of probe Jacobians; we also support multi-corpus subspaces by concatenating corpora prior to PCA. Columns of $U_\ell$ are orthonormalized.

\subsection{Constrained surrogate and KKT view}
We model a PPO step by the quadratic surrogate with linear representation constraints
\begin{equation}
\min_{\Delta\theta}\ \langle \mathbf g,\Delta\theta\rangle + \tfrac{1}{2}\Delta\theta^\top \mathbf H \Delta\theta
\quad\text{s.t.}\quad \mathbf C_\ell \Delta\theta = 0,\ \forall \ell\in\mathcal L.
\label{eq:surrogate}
\end{equation}
$\mathbf g$ is the gradient of the PPO objective and $\mathbf H$ a local curvature (Fisher/GAE approximation). The constraints $\mathbf C_\ell$ encode that first-order changes to activations at layer $\ell$ have no component in $\mathrm{span}(U_\ell)$. Stacking constraints into $\mathbf C$, the KKT system yields
\begin{equation}
\Delta\theta^\star = -\Pi_{\mathrm{null}(\mathbf C)}\, \mathbf H^{-1}\mathbf g,
\quad
\Pi_{\mathrm{null}(\mathbf C)} = \mathbf I - \mathbf C^\top(\mathbf C \mathbf C^\top)^{-1}\mathbf C.
\label{eq:kkt}
\end{equation}
Under block-diagonal $\mathbf H$ and isotropic blocks ($\mathbf H \!\approx\! \alpha \mathbf I$ per module), the solution simplifies to a \emph{projected gradient step} with $\Delta\theta^\star \!=\! -\alpha^{-1}\Pi_{\mathrm{null}(\mathbf C)}\mathbf g$.

\subsection{Activation-scoped gradient projection on LoRA (\asproj)}
For a layer with hidden size $d$ and subspace $U_\ell$, define $P_\ell\!=\!I-U_\ell U_\ell^\top$. Let $B\!\in\!\bbR^{d\times r}$ and $A\!\in\!\bbR^{r\times d}$ be LoRA matrices. Denote their gradients $\nabla B$ and $\nabla A$. We project by
\begin{equation}
\nabla B \leftarrow P_\ell\, \nabla B,\qquad
\nabla A \leftarrow \nabla A\, P_\ell.
\label{eq:proj}
\end{equation}
We apply left-multiplication when the \emph{output} dimension equals $d$ (e.g., $B$), and right-multiplication when the \emph{input} dimension equals $d$ (e.g., $A$). This realizes the per-block projection in Eq.~\eqref{eq:kkt}.

\begin{proposition}[First-order invariance]\label{prop:invariance}
Under local linearization of the LoRA block, a gradient step using Eq.~\eqref{eq:proj} ensures that for tokens flowing through the protected modules, the induced change $\delta h_\ell$ at layer $\ell$ satisfies $U_\ell^\top \delta h_\ell = 0$.
\end{proposition}
\begin{proof}[Sketch]
Let $h_{\ell+1}\!\approx\!(W+\Delta W)h_\ell$ with $\Delta W\!=\!BA$. The first-order change is $\delta h_{\ell+1}\!\approx\!(\delta B)Ah_\ell + B(\delta A)h_\ell$. With Eq.~\eqref{eq:proj}, $(\delta B)$ lies in the row space of $P_\ell$ and $(\delta A)h_\ell$ lies in the column space of $P_\ell$, hence $\delta h_{\ell+1}\!\in\!\mathrm{im}(P_\ell)$ and $U_\ell^\top \delta h_{\ell+1}\!=\!0$.
\end{proof}

\begin{proposition}[KKT consistency]\label{prop:kkt}
Assume block-diagonal $\mathbf H$ with isotropic blocks and linear constraints that decouple per module. Then Eq.~\eqref{eq:proj} is the exact per-block KKT solution to Eq.~\eqref{eq:surrogate}.
\end{proposition}

\paragraph{Trade-off diagnostic.} Let $\Pi$ be the projector that implements Eq.~\eqref{eq:proj} at parameter level. The improvement gap between unconstrained and constrained steps at equal step size $\eta$ is
\begin{equation}
\Delta\mathcal{L} \;\approx\; \tfrac{\eta}{2}\big\| (I-\Pi)\mathbf g \big\|_2^2,
\label{eq:tradeoff}
\end{equation}
the \emph{removed-gradient energy}. We log this during training to quantify constraint cost.

\paragraph{Soft schedule and dual control.} Early in training, we optionally use a soft projector $(I-\gamma U_\ell U_\ell^\top)$ with $\gamma\!\uparrow\!1$. Alternatively, we maintain a target \cka\ level and adapt $\lambda$ by dual averaging, increasing it when the observed $1-\mathrm{CKA}$ exceeds target.

\subsection{Representation anchor via linear \cka}
Given token-level representations $H_\ell^{\pi_\theta}, H_\ell^{\pi_0}\!\in\!\bbR^{T\times d}$ for concatenated prompt+response, linear \cka\ is
\begin{equation}
\mathrm{CKA}(X,Y) \;=\; \frac{\langle X_c X_c^\top,\ Y_c Y_c^\top\rangle_F}{\|X_c X_c^\top\|_F\,\|Y_c Y_c^\top\|_F+\varepsilon},
\end{equation}
where $X_c$ and $Y_c$ are mean-centered across tokens. We define a per-sample penalty
\begin{equation}
\mathcal{L}_{\mathrm{rep}}(x,y) \;=\; \frac{1}{|\mathcal L|}\sum_{\ell\in\mathcal L}\big(1-\mathrm{CKA}(H_\ell^{\pi_\theta}(x,y),H_\ell^{\pi_0}(x,y))\big),
\end{equation}
and subtract $\lambda \mathcal{L}_{\mathrm{rep}}$ from the scalar reward before PPO. We compute \cka\ on response tokens by default (prompt-only reported in Appendix).

\subsection{Complexity and implementation}
Projection multiplies a LoRA gradient by $P_\ell$ on the matching dimension, costing $O(dr)$ per affected matrix (rank $r\!\ll\!d$). We precompute $P_\ell$ and register lightweight gradient hooks. \cka\ requires two forward passes (policy and reference) on a small batch, amortized every $k$ PPO steps. We implement \ours\ in TRL~\citep{trl} with value head, KL control, and public reward models.

\section{Evaluation protocol (\taxbench)}
\label{sec:evalproto}
\paragraph{Prompts and rewards.} Prompts from UltraFeedback (binarized, cleaned)~\citep{ultrafeedback}, StackExchange-Paired~\citep{stackexchangepaired}, and HH-RLHF~\citep{hh_rlhf}. Reward is from a public DeBERTa-v3 model~\citep{oa_rm}. We also report a second RM for RM-agnosticity (Appendix).

\paragraph{Capability corpora and subspaces.} We build subspaces from GSM8K questions~\citep{cobbe2021gsm8k} and WikiText-103~\citep{merity2016wikitext}. Unless stated, we protect mid layers (e.g., 8/16/24) with rank $r\!=\!32$.

\paragraph{Models and stack.} LLaMA-3--8B base~\citep{llama3}; TRL PPO with LoRA on attention/MLP projections and KL control to a frozen reference. Training details are in Appendix~\S\ref{app:details}.

\paragraph{Metrics.} (i) \textbf{Reward@KL}: mean RM score at matched KL budgets and matched response-length bins; (ii) \textbf{Perplexity} on WikiText-103 (lower is better); (iii) \textbf{GSM8K} 5-shot accuracy; (iv) \textbf{\reptax}: mean $1-\mathrm{CKA}$ over selected layers on a held-out corpus (response tokens); (v) \textbf{Removed-gradient energy} $\|(I-\Pi)\mathbf g\|_2^2$; (vi) \textbf{Safety panel} (toxicity/refusal; Appendix). All results averaged over $\geq$3 seeds with 95\% CIs.

\paragraph{Fairness protocol.} We \emph{match KL} across methods; normalize for response length; fix generation hyperparameters; bootstrap win-rates; and report seed variability. We ensure subspace corpora are disjoint from preference prompts.

\section{Experiments}
\label{sec:exp}
\paragraph{Main results.} Table~\ref{tab:main} and Fig.~\ref{fig:pareto} show that \ours\ achieves higher Reward@KL while reducing PPL regression and preserving GSM8K accuracy, with lower \reptax. At equal reward, \ours\ improves capability metrics; at equal KL, \ours\ improves reward.

\begin{table}[t]
\centering
\caption{Main results (placeholders; replace with measured values). \ours\ reduces alignment tax at matched KL.}
\label{tab:main}
\begin{tabular}{lccccc}
\toprule
Method & Reward@KL $\uparrow$ & PPL (WT103) $\downarrow$ & GSM8K (\%) $\uparrow$ & \reptax\ $\downarrow$ & Removed-grad $\downarrow$\\
\midrule
PPO (LoRA) & \emph{X} & \emph{X} & \emph{X} & \emph{X} & \emph{X}\\
+ LM replay & \emph{X} & \emph{X} & \emph{X} & \emph{X} & \emph{X}\\
O-LoRA style ortho & \emph{X} & \emph{X} & \emph{X} & \emph{X} & \emph{X}\\
Model soup (post-hoc) & \emph{X} & \emph{X} & \emph{X} & \emph{X} & \emph{X}\\
\textbf{\ours} (\asproj) & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}}\\
\textbf{\ours} (\asproj+\cka) & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}}\\
\bottomrule
\end{tabular}
\end{table}

\begin{figure}[t]
\centering
\includegraphics[width=.75\linewidth]{fig_pareto_placeholder.pdf}
\vspace{-2mm}
\caption{Reward--capability Pareto curves at matched KL (placeholder). \ours\ shifts the frontier outward.}
\label{fig:pareto}
\vspace{-1mm}
\end{figure}

\paragraph{Overhead.} Table (Appendix) reports wall-clock, memory, CKA frequency $k$, and LoRA ranks. Projection cost is negligible; \cka\ every 2--5 steps adds modest overhead.

\subsection{Subspace validity}
\label{sec:subspace_validity}
We evaluate whether $U_\ell$ captures capability structure:
\begin{enumerate}[leftmargin=4mm,itemsep=1pt]
\item \textbf{Random-$U$ control:} replace $U_\ell$ by random orthonormal bases; performance does not improve.
\item \textbf{Probe-$U$:} train linear probes for math/code; use top probe directions; retention is comparable or better.
\item \textbf{Multi-corpus:} protecting math vs LM vs both shows additivity (protect-math preserves GSM8K; protect-both preserves both).
\item \textbf{Refresh:} re-estimating $U_\ell$ mid-training yields minimal change (Appendix).
\end{enumerate}

\subsection{RM-agnosticity and human evaluation}
We repeat a subset with a second open RM and report rank correlations of method deltas; a small human side-by-side (100 prompts) corroborates trends (Appendix). Length distributions are matched across bins.

\subsection{DPO generality (AS-DPO)}
We replace PPO with DPO~\citep{rafailov2023dpo}, apply \asproj\ to SFT gradients, and add \cka\ as an auxiliary loss. Trends mirror PPO on UltraFeedback.

\subsection{Ablations}
We sweep protected layers, rank $r$, \cka\ weight $\lambda$, projection schedule $\gamma$, CKA frequency $k$, and token subsets (prompt-only vs response-only). We also ablate LoRA target modules.

\section{Analysis}
\label{sec:analysis}
\paragraph{Pareto improvements.} \ours\ expands the Reward--Capability frontier beyond stronger KL and LM replay. Improvements persist across prompts and RMs. Removed-gradient energy correlates with reward deltas, validating Eq.~\eqref{eq:tradeoff}.

\paragraph{Why it works.} \asproj\ prevents spending gradient on directions predictive of protected capabilities, while \cka\ discourages disruptive internal re-routing that policy-space KL does not directly constrain.

\section{Related work}
\label{sec:related}
\textbf{RLHF.} Preference optimization with PPO and KL control~\citep{ziegler2019fine,stiennon2020learning,ouyang2022instruct,schulman2017ppo}. \textbf{DPO.} Supervised alternatives to RL~\citep{rafailov2023dpo}. \textbf{PEFT.} LoRA for efficient tuning~\citep{hu2021lora}. \textbf{Representation similarity.} CKA comparisons across layers~\citep{kornblith2019similarity}. \textbf{Mitigating alignment tax.} Mixing LM loss, stronger KL, and post-hoc averaging~\citep{wortsman2022model}. Our method differs by constraining \emph{representation directions} during \emph{RLHF updates} and adding a representation trust region.

\section{Limitations and risks}
\label{sec:limitations}
\textbf{Subspace mis-specification.} PCA may capture variance unrelated to capability; we include probe-defined and random-$U$ controls, and report removed-gradient energy to detect over-constraint. \textbf{Reward misspecification.} Our approach does not prevent reward hacking; RM diversity and human checks are advised. \textbf{Compute.} \cka\ adds forward passes; we amortize costs.

\section{Broader impact}
By reducing collateral damage to base capabilities during alignment, \ours\ can keep systems broadly useful. Practitioners should choose benign corpora and evaluate safety; subspace protection could retain harmful skills if misapplied.

\section*{Reproducibility statement}
We provide scripts for TRL PPO training, subspace building (GSM8K/WikiText-103), and perplexity evaluation. Seeds, hyperparameters, datasets, and model identifiers are specified. We will release logs and checkpoints upon acceptance.

\appendix
\section{KKT derivations and proofs}
\label{app:kkt}
We expand the KKT system for Eq.~\eqref{eq:surrogate}; under block-diagonal $\mathbf H$ and per-module constraints, the solution reduces to Eq.~\eqref{eq:kkt}. We detail the mapping from representation constraints to parameter-level projectors for common Transformer linear layers and LoRA parameterizations.

\section{Implementation details}
\label{app:details}
We list TRL settings (batching, KL coefficients, value head), LoRA targets, CKA frequency and layers, subspace ranks, and decoding parameters. We clarify length binning and KL matching for \taxbench.

\section{Datasets and safety panel}
We describe prompt sources (UltraFeedback/StackExchange/HH-RLHF), RM configurations, de-duplication, and safety metrics (toxicity/refusal).

\section{AS-DPO details}
We present the AS-DPO objective, loss decomposition, and training recipe, plus a small-scale result on UltraFeedback.

\bibliography{refs}
\bibliographystyle{iclr2025_conference}

\end{document}