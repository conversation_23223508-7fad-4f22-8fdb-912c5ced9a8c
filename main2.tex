\documentclass{article}
\usepackage{iclr2025_conference,times}
\usepackage{hyperref}
\usepackage{url}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{amsmath,amssymb,amsthm,mathtools}
\usepackage{microtype}
\usepackage{enumitem}
\usepackage{multirow}
\usepackage{float}
\usepackage{subcaption}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{xcolor}
\usepackage{tikz}
\usetikzlibrary{positioning,arrows.meta,fit,calc}

% Uncomment for camera ready
% \iclrfinalcopy

\title{Activation-Scoped RLHF: Aligning Without Forgetting via Subspace-Constrained Updates and Representation Anchors}

\author{Anonymous authors\\
Anonymous affiliation\\
\texttt{<EMAIL>}}

% Macros
\newcommand{\ours}{\textsc{AS-RLHF}}
\newcommand{\asproj}{\textsc{AS-Proj}}
\newcommand{\cka}{\textsc{CKA}}
\newcommand{\reptax}{\textsc{RepTax}}
\newcommand{\taxbench}{\textsc{TaxBench++}}
\newcommand{\kl}{\mathrm{KL}}
\newcommand{\E}{\mathbb{E}}
\newcommand{\bbR}{\mathbb{R}}
\theoremstyle{plain}
\newtheorem{proposition}{Proposition}
\newtheorem{lemma}{Lemma}

\begin{document}
\maketitle

\begin{abstract}
Post-training with human preferences (RLHF/DPO) improves helpfulness and safety but can degrade base capabilities such as language modeling, mathematical reasoning, or knowledge recall---\emph{alignment tax}. We introduce \emph{Activation-Scoped RLHF} (\ours), a drop-in enhancement to PPO-style RLHF that preserves task-critical \emph{capability subspaces} while aligning to feedback. \ours\ has two parts: (i) \asproj, which projects \emph{LoRA} gradients onto the orthogonal complement of per-layer capability subspaces estimated from unlabeled corpora; and (ii) a \emph{representation anchor} that subtracts a linear-\cka\ penalty between the policy and a frozen reference from the scalar reward. We formalize a constrained quadratic surrogate for PPO and show our projections implement the per-block KKT solution under mild curvature assumptions; we also prove first-order invariance of protected directions and derive a \emph{removed-gradient energy} diagnostic that predicts reward trade-offs. Plugging \ours\ into public RLHF stacks (TRL PPO) yields systematic reward--capability Pareto improvements on public prompts and reward models while reducing representational drift. Code and evaluation scripts are provided for full reproducibility.
\end{abstract}

\section{Introduction}
\label{sec:intro}
Large language models (LLMs) are commonly aligned to human preferences via supervised fine-tuning (SFT) and reinforcement learning from human feedback (RLHF). While alignment improves safety and helpfulness, it often incurs an \emph{alignment tax}: degradation of general capabilities such as language modeling (LM) quality, mathematical reasoning, or knowledge recall. Practitioners typically mitigate this with stronger KL penalties, mixing LM losses, or post-hoc model averaging. These remedies either reduce achievable reward or require extra engineering, and they do not provide targeted protection for specific capabilities.

\paragraph{Goal.} Align without forgetting: preserve identified capabilities while optimizing preference reward.

\paragraph{Key idea.} Control \emph{where} learning happens. If we identify per-layer subspaces predictive of a capability, we can prevent first-order movement in those directions while allowing adaptation elsewhere; additionally, we softly anchor internal activations to a frozen reference using a representation-similarity penalty. This combination is complementary to policy-space KL and requires no changes to data or reward models.

\paragraph{Contributions.} We propose \ours, comprising:
\begin{enumerate}[leftmargin=4mm,itemsep=1pt,topsep=1pt]
\item \textbf{Subspace-constrained updates (\asproj).} We estimate capability subspaces $U_\ell\!\in\!\bbR^{d\times r}$ from unlabeled corpora (e.g., GSM8K questions for math; WikiText-103 for LM). During PPO with LoRA, we multiply LoRA gradients by $P_\ell\!=\!I-U_\ell U_\ell^\top$ on appropriate dimensions, forbidding first-order movement in protected directions.
\item \textbf{Representation anchor.} We subtract $\lambda(1-\mathrm{CKA})$ between the policy and a frozen reference on selected layers from the scalar reward, providing a representation-space trust region complementary to policy-space KL.
\item \textbf{Theory \& diagnostics.} We formalize a constrained quadratic PPO surrogate and show our projections are the per-block KKT solution under isotropic curvature. We prove first-order invariance of protected directions, and derive a \emph{removed-gradient energy} diagnostic predicting reward trade-offs.
\item \textbf{Fair evaluation.} We define \taxbench: reward at matched KL and matched length bins, WikiText-103 PPL, GSM8K accuracy, \reptax{} (mean $1-\mathrm{CKA}$), and removed-gradient energy. Baselines include stronger KL, LM replay, orthogonality-only LoRA, and model soup; we also provide a DPO variant (AS-DPO) to demonstrate optimizer-agnosticity.
\end{enumerate}

\begin{figure}[t]
\centering
\begin{tikzpicture}[node distance=1.1cm]
\tikzstyle{box}=[draw, rounded corners, align=center, inner sep=3pt, minimum width=2.7cm, minimum height=0.8cm]
\node[box, fill=white] (prompt) {Prompt $x$};
\node[box, right=1.8cm of prompt, fill=white] (policy) {Policy $\pi_\theta$ (LoRA)};
\node[box, right=1.8cm of policy, fill=white] (rm) {Reward model $R$};
\node[box, below=1.2cm of policy, fill=white] (ref) {Frozen ref $\pi_{\theta_0}$};
\node[box, below=1.2cm of rm, fill=white] (cka) {Linear-\cka\ Penalty};
\draw[-{Latex}] (prompt) -- node[above]{sample $y$} (policy);
\draw[-{Latex}] (policy) -- node[above]{score $R(x,y)$} (rm);
\draw[-{Latex}] (policy) -- node[right]{hidden states} (cka);
\draw[-{Latex}] (ref) -- node[left]{hidden states} (cka);
\draw[-{Latex}] (cka) -- node[right]{$-\lambda(1-\mathrm{CKA})$} (rm);
\node[box, below=1.2cm of prompt, xshift=1.8cm, fill=white] (sub) {Subspaces $U_\ell$ (PCA / probes)};
\node[box, below=1.2cm of policy, xshift=-2.2cm, fill=white] (proj) {\asproj: $g\!\leftarrow\!P_\ell g$};
\draw[-{Latex}] (sub) -- node[below]{build $P_\ell=I-U_\ell U_\ell^\top$} (proj);
\draw[-{Latex}] (proj) -- node[below]{project LoRA grads} (policy);
\end{tikzpicture}
\vspace{-2mm}
\caption{\textbf{\ours\ overview.} We add (i) \emph{activation-scoped} projection of LoRA gradients onto $U_\ell^\perp$, and (ii) a \emph{representation anchor} that subtracts a linear-\cka\ penalty from rewards using hidden states of the policy and a frozen reference.}
\label{fig:overview}
\vspace{-1mm}
\end{figure}

\section{Background}
\label{sec:background}
\paragraph{RLHF with PPO.} RLHF fine-tunes a policy $\pi_\theta$ to maximize a proxy reward $R(x,y)$ while constraining divergence from a reference $\pi_{\mathrm{ref}}$ via KL. Given prompts $x$ and sampled responses $y\!\sim\!\pi_\theta(\cdot|x)$, PPO updates $\theta$ using a clipped objective and a learned value head.

\paragraph{LoRA.} Low-rank adapters introduce trainable matrices into linear projections, enabling memory-efficient tuning. Let $W$ be frozen and $\Delta W\!=\!BA$ be the low-rank update; gradients flow through $A$ and $B$.

\paragraph{Representation similarity via CKA.} Linear \cka\ compares centered Gram matrices of token representations. We use $(1-\mathrm{CKA})$ as a penalty that is small when policy activations match the reference on selected layers.

\section{Method: Activation-Scoped RLHF}
\label{sec:method}
\subsection{Identifying capability subspaces}
Given layer index $\ell$ and a capability corpus $\mathcal{D}$ (e.g., GSM8K questions for math; WikiText-103 for general LM), we collect hidden states $H_\ell\!\in\!\bbR^{N\times d}$ from the frozen base model and compute the top-$r$ PCs $U_\ell\!\in\!\bbR^{d\times r}$. We optionally use \emph{probe-defined} subspaces (train linear probes to predict math/code labels and take top singular directions of the probe Jacobians), and support multi-corpus subspaces by concatenating corpora prior to PCA. Columns of $U_\ell$ are orthonormalized.

\subsection{Constrained surrogate and KKT view}
We model a PPO step by the quadratic surrogate with linear representation constraints
\begin{equation}
\min_{\Delta\theta}\ \langle \mathbf g,\Delta\theta\rangle + \tfrac{1}{2}\Delta\theta^\top \mathbf H \Delta\theta
\quad\text{s.t.}\quad \mathbf C_\ell \Delta\theta = 0,\ \forall \ell\in\mathcal L.
\label{eq:surrogate}
\end{equation}
$\mathbf g$ is the gradient of the PPO objective and $\mathbf H$ a local curvature (Fisher/GAE approximation). The constraints $\mathbf C_\ell$ encode that first-order changes to activations at layer $\ell$ have no component in $\mathrm{span}(U_\ell)$. Stacking constraints into $\mathbf C$, the KKT system yields
\begin{equation}
\Delta\theta^\star = -\Pi_{\mathrm{null}(\mathbf C)}\, \mathbf H^{-1}\mathbf g,
\quad
\Pi_{\mathrm{null}(\mathbf C)} = \mathbf I - \mathbf C^\top(\mathbf C \mathbf C^\top)^{-1}\mathbf C.
\label{eq:kkt}
\end{equation}
Under block-diagonal $\mathbf H$ and isotropic blocks ($\mathbf H \!\approx\! \alpha \mathbf I$ per module), the solution simplifies to a \emph{projected gradient step} with $\Delta\theta^\star \!=\! -\alpha^{-1}\Pi_{\mathrm{null}(\mathbf C)}\mathbf g$.

\subsection{Activation-scoped gradient projection on LoRA (\asproj)}
For a layer with hidden size $d$ and subspace $U_\ell$, define $P_\ell\!=\!I-U_\ell U_\ell^\top$. Let $B\!\in\!\bbR^{d\times r}$ and $A\!\in\!\bbR^{r\times d}$ be LoRA matrices. Denote their gradients $\nabla B$ and $\nabla A$. We project by
\begin{equation}
\nabla B \leftarrow P_\ell\, \nabla B,\qquad
\nabla A \leftarrow \nabla A\, P_\ell.
\label{eq:proj}
\end{equation}
We apply left-multiplication when the \emph{output} dimension equals $d$ (e.g., $B$), and right-multiplication when the \emph{input} dimension equals $d$ (e.g., $A$).

\begin{proposition}[First-order invariance]\label{prop:invariance}
Under local linearization of the LoRA block, a gradient step using Eq.~\eqref{eq:proj} ensures that for tokens flowing through the protected modules, the induced change $\delta h_\ell$ at layer $\ell$ satisfies $U_\ell^\top \delta h_\ell = 0$.
\end{proposition}
\begin{proof}[Sketch]
Let $h_{\ell+1}\!\approx\!(W+\Delta W)h_\ell$ with $\Delta W\!=\!BA$. The first-order change is $\delta h_{\ell+1}\!\approx\!(\delta B)Ah_\ell + B(\delta A)h_\ell$. With Eq.~\eqref{eq:proj}, $(\delta B)$ lies in the row space of $P_\ell$ and $(\delta A)h_\ell$ lies in the column space of $P_\ell$, hence $\delta h_{\ell+1}\!\in\!\mathrm{im}(P_\ell)$ and $U_\ell^\top \delta h_{\ell+1}\!=\!0$.
\end{proof}

\begin{proposition}[KKT consistency]
Assume block-diagonal $\mathbf H$ with isotropic blocks and linear constraints that decouple per module. Then Eq.~\eqref{eq:proj} is the exact per-block KKT solution to Eq.~\eqref{eq:surrogate}.
\end{proposition}

\paragraph{Trade-off diagnostic.} Let $\Pi$ be the projector that implements Eq.~\eqref{eq:proj} at parameter level. The improvement gap between unconstrained and constrained steps at equal step size $\eta$ is
\begin{equation}
\Delta\mathcal{L} \;\approx\; \tfrac{\eta}{2}\big\| (I-\Pi)\mathbf g \big\|_2^2,
\label{eq:tradeoff}
\end{equation}
the \emph{removed-gradient energy}. We log this during training to quantify constraint cost. Early in training, we optionally use a soft projector $(I-\gamma U_\ell U_\ell^\top)$ with $\gamma\!\uparrow\!1$, or adapt the \cka\ weight $\lambda$ via dual averaging to target a desired representation similarity.

\subsection{Representation anchor via linear \cka}
Given token-level representations $H_\ell^{\pi_\theta}, H_\ell^{\pi_0}\!\in\!\bbR^{T\times d}$ for concatenated prompt+response, linear \cka\ is
\begin{equation}
\mathrm{CKA}(X,Y) \;=\; \frac{\langle X_c X_c^\top,\ Y_c Y_c^\top\rangle_F}{\|X_c X_c^\top\|_F\,\|Y_c Y_c^\top\|_F+\varepsilon},
\end{equation}
where $X_c$ and $Y_c$ are mean-centered across tokens. We define a per-sample penalty
\begin{equation}
\mathcal{L}_{\mathrm{rep}}(x,y) \;=\; \frac{1}{|\mathcal L|}\sum_{\ell\in\mathcal L}\big(1-\mathrm{CKA}(H_\ell^{\pi_\theta}(x,y),H_\ell^{\pi_0}(x,y))\big),
\end{equation}
and subtract $\lambda \mathcal{L}_{\mathrm{rep}}$ from the scalar reward before PPO. We compute \cka\ on response tokens by default (prompt-only reported in Appendix). \cka\ is invariant to orthonormal transformations, penalizing anisotropic distortions but not benign rotations, complementing policy-space KL.

\subsection{Algorithm}
Algorithm~\ref{alg:asppo} summarizes the full training loop.
\begin{algorithm}[H]
\caption{\ours: PPO with activation-scoped projection and \cka\ reward shaping}
\label{alg:asppo}
\begin{algorithmic}[1]
\Require Policy with value head $\pi_\theta$, frozen reference $\pi_{\theta_0}$, reward model $R$, subspaces $\{U_\ell\}$, protected layers $\mathcal{L}$, weight $\lambda$
\State Precompute $P_\ell = I-U_\ell U_\ell^\top$; register LoRA grad hooks: $\nabla \leftarrow P_\ell \nabla$ or $\nabla \leftarrow \nabla P_\ell$
\For{batches of prompts $x$}
    \State Sample responses $y \sim \pi_\theta(\cdot|x)$
    \State $r \leftarrow R(x,y)$ \Comment{scalar reward}
    \State $\mathrm{pen} \leftarrow \frac{1}{|\mathcal{L}|}\sum_{\ell \in \mathcal{L}} \big(1-\mathrm{CKA}(H_\ell^{\pi_\theta},H_\ell^{\pi_{\theta_0}})\big)$ (every $k$ steps)
    \State $\tilde{r} \leftarrow r - \lambda \cdot \mathrm{pen}$
    \State \textsc{PPOUpdate}$(\pi_\theta,\tilde{r})$ with KL control to $\pi_{\theta_0}$
\EndFor
\end{algorithmic}
\end{algorithm}

\section{Evaluation protocol (\taxbench)}
\label{sec:evalproto}
\paragraph{Prompts and rewards.} Prompts from UltraFeedback (binarized, cleaned), StackExchange-Paired, and HH-RLHF. Reward from a public DeBERTa-v3 reward model. A second reward model and a small human check are included in Appendix for RM-agnosticity.

\paragraph{Capability corpora and subspaces.} Subspaces from GSM8K questions and WikiText-103 (mid layers 8/16/24, rank $r\!=\!32$ unless noted). We ensure subspace corpora are disjoint from prompt sources.

\paragraph{Models and stack.} LLaMA-3--8B base; TRL PPO with value head and LoRA on attention/MLP projections; KL control to a frozen reference. Implementation matches the public scripts.

\paragraph{Metrics.} (i) \textbf{Reward@KL}: mean RM score at matched KL budgets and matched response-length bins; (ii) \textbf{Perplexity} on WikiText-103; (iii) \textbf{GSM8K} 5-shot accuracy; (iv) \textbf{\reptax}: mean $1-\mathrm{CKA}$ over selected layers on a held-out corpus (response tokens); (v) \textbf{Removed-gradient energy} $\|(I-\Pi)\mathbf g\|_2^2$; (vi) \textbf{Safety panel} (toxicity/refusal; Appendix). All results averaged over $\geq$3 seeds with 95\% CIs.

\paragraph{Fairness protocol.} We \emph{match KL} across methods; normalize for response length; fix generation hyperparameters; bootstrap win-rates; and report seed variability. Hyperparameters, seeds, and dataset versions are listed in the Appendix.

\section{Experiments}
\label{sec:exp}
\paragraph{Main results.} Table~\ref{tab:main} reports Reward@KL, WT103 PPL, GSM8K accuracy, \reptax, and removed-gradient energy. Fig.~\ref{fig:pareto} shows reward vs capability under matched KL bins. \ours\ improves reward for the same KL while reducing PPL regression and preserving GSM8K---a Pareto shift.

\begin{table}[t]
\centering
\caption{Main results (placeholders; replace with measured values). \ours\ reduces alignment tax at matched KL.}
\label{tab:main}
\begin{tabular}{lccccc}
\toprule
Method & Reward@KL $\uparrow$ & PPL (WT103) $\downarrow$ & GSM8K (\%) $\uparrow$ & \reptax\ $\downarrow$ & Removed-grad $\downarrow$\\
\midrule
PPO (LoRA) & \emph{X} & \emph{X} & \emph{X} & \emph{X} & \emph{X}\\
+ LM replay & \emph{X} & \emph{X} & \emph{X} & \emph{X} & \emph{X}\\
O-LoRA style ortho & \emph{X} & \emph{X} & \emph{X} & \emph{X} & \emph{X}\\
Model soup (post-hoc) & \emph{X} & \emph{X} & \emph{X} & \emph{X} & \emph{X}\\
\textbf{\ours} (\asproj) & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}}\\
\textbf{\ours} (\asproj+\cka) & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}} & \textbf{\emph{X}}\\
\bottomrule
\end{tabular}
\end{table}

\begin{figure}[t]
\centering
% TikZ placeholder for Pareto curve (no external figure needed)
\begin{tikzpicture}[scale=1.0]
\draw[step=0.5cm, gray!20, very thin] (0,0) grid (7,4);
\draw[->] (0,0) -- (7.2,0) node[right] {KL (matched bins)};
\draw[->] (0,0) -- (0,4.2) node[above] {Reward (shaped)};
% baseline points
\fill (1.0,1.1) circle (2pt);
\fill (2.0,1.6) circle (2pt);
\fill (3.0,1.8) circle (2pt);
% ours points (higher)
\draw[blue, thick] (1.0,1.3) -- (2.0,1.9) -- (3.0,2.2);
\fill[blue] (1.0,1.3) circle (2pt);
\fill[blue] (2.0,1.9) circle (2pt);
\fill[blue] (3.0,2.2) circle (2pt);
\end{tikzpicture}
\vspace{-2mm}
\caption{Reward--KL scatter (placeholder). \ours\ shifts the frontier upward at matched KL.}
\label{fig:pareto}
\vspace{-1mm}
\end{figure}

\subsection{Subspace validity}
\label{sec:subspace_validity}
We probe whether $U_\ell$ captures capability structure: (i) \textbf{Random-$U$ control}: replace $U_\ell$ with random orthonormal bases; performance does not improve; (ii) \textbf{Probe-$U$}: train linear probes for math/code and use top probe directions; retention is comparable or better; (iii) \textbf{Multi-corpus}: protecting math vs LM vs both shows additivity; (iv) \textbf{Refresh}: re-estimating $U_\ell$ mid-training yields minimal change (Appendix).

\subsection{RM-agnosticity and human evaluation}
We repeat a subset with a second open reward model and report rank-correlations of method deltas; a small human side-by-side (100 prompts) corroborates trends (Appendix). Length distributions are matched across bins to avoid verbosity confounds.

\subsection{DPO generality (AS-DPO)}
We replace PPO with DPO, apply \asproj\ to SFT gradients, and add \cka\ as an auxiliary loss. Trends mirror PPO on UltraFeedback.

\subsection{Ablations and overhead}
We sweep protected layers, rank $r$, \cka\ weight $\lambda$, projection schedule $\gamma$, CKA frequency $k$, and token subsets (prompt-only vs response-only). We also ablate LoRA target modules. Projection cost is negligible ($O(dr)$ per matrix); \cka\ every 2--5 steps adds modest overhead (Appendix table).

\section{Analysis}
\label{sec:analysis}
\paragraph{Pareto improvements.} \ours\ expands the Reward--Capability frontier beyond stronger KL and LM replay. Improvements persist across prompts and reward models.

\paragraph{Why it works.} \asproj\ prevents spending gradient on directions predictive of protected capabilities, while \cka\ discourages disruptive internal re-routing that policy-space KL does not directly constrain.

\section{Related work}
\label{sec:related}
\textbf{RLHF.} Preference optimization with PPO and KL control; \textbf{DPO.} Supervised alternatives to RL; \textbf{PEFT.} LoRA for efficient tuning; \textbf{Representation similarity.} CKA comparisons across layers; \textbf{Mitigating alignment tax.} Mixing LM loss, stronger KL, and post-hoc averaging. Our method differs by constraining updates in capability-aligned \emph{representation} subspaces during RLHF and adding a representation trust region.

\section{Limitations and risks}
\label{sec:limitations}
\textbf{Subspace mis-specification.} PCA may capture variance unrelated to capability; we include probe-defined and random-$U$ controls, and report removed-gradient energy to detect over-constraint. \textbf{Reward misspecification.} Our approach does not prevent reward hacking; RM diversity and human checks are advised. \textbf{Compute.} \cka\ adds forward passes; we amortize costs.

\section{Broader impact}
By reducing collateral damage to base capabilities during alignment, \ours\ can keep systems broadly useful. Practitioners should choose benign corpora and evaluate safety; subspace protection could retain harmful skills if misapplied.

\section*{Reproducibility statement}
We provide TRL PPO training scripts (with \asproj\ and optional \cka), subspace building (GSM8K/WikiText-103), evaluation (PPL, GSM8K, \reptax), and plotting utilities. Seeds, hyperparameters, dataset/model identifiers, and logs are included in configs and code. We will release checkpoints upon acceptance.

\bibliography{refs}
\bibliographystyle{iclr2025_conference}

% ------------------ APPENDIX (excluded from 9-page cap) ------------------
\appendix
\section{KKT derivations and proofs}
We expand the KKT system for Eq.~\eqref{eq:surrogate}; under block-diagonal $\mathbf H$ and per-module constraints, the solution reduces to Eq.~\eqref{eq:kkt}. We detail the mapping from representation constraints to parameter-level projectors for Transformer linear layers and LoRA parameterizations.

\section{Implementation details}
We list TRL settings (batching, KL coefficients, value head), LoRA targets, CKA frequency and layers, subspace ranks, and decoding parameters. We clarify length binning and KL matching for \taxbench.

\section{Datasets and safety panel}
We describe prompt sources (UltraFeedback/StackExchange/HH-RLHF), reward model configurations, de-duplication, and safety metrics (toxicity/refusal).

\section{AS-DPO details}
We present the AS-DPO objective, loss decomposition, and training recipe, plus a small-scale result on UltraFeedback.

\end{document}