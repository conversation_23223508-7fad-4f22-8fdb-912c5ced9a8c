
# BAG: Budget-Aware Gating — ICLR-style Paper Package

This folder contains a complete LaTeX draft of the ICLR paper:
**"BAG: Budget-Aware Gating for Capability-Budgeted Multi-Objective Alignment"**.

## Structure
- `main.tex` – entry point (ICLR style)
- `sections/` – Introduction, Method, Theory, Experiments, Related Work, Limitations, Conclusion, Appendix
- `refs.bib` – BibTeX entries for works cited in the draft

## How to compile
1. Download the ICLR 2025 style files (e.g., `iclr2025_conference.sty`) into the same directory as `main.tex`.
2. Compile with `pdflatex` / `bibtex`:
   ```bash
   pdflatex main && bibtex main && pdflatex main && pdflatex main
   ```

## Notes
- The experimental section is written so you can **drop in results** once you run the BAG codebase.
- Replace the placeholder “Anonymous” entries with correct metadata when ready.
- The method text and pseudocode match the "BAG" design we've discussed (mixture-of-distributions DPO + budget via barrier/dual).

Happy writing!
