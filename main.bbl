\begin{thebibliography}{18}
\providecommand{\natexlab}[1]{#1}
\providecommand{\url}[1]{\texttt{#1}}
\expandafter\ifx\csname urlstyle\endcsname\relax
  \providecommand{\doi}[1]{doi: #1}\else
  \providecommand{\doi}{doi: \begingroup \urlstyle{rm}\Url}\fi

\bibitem[Anonymous(2024)]{disperse2024}
Anonymous.
\newblock Disperse-then-merge: Pushing the limits of instruction tuning via alignment tax reduction.
\newblock \emph{Findings of ACL}, 2024.

\bibitem[Anonymous(2025)]{bfpo2024}
Anonymous.
\newblock Bi-factorial preference optimization: Balancing safety and helpfulness.
\newblock \emph{OpenReview}, 2025.

\bibitem[<PERSON>(2004)<PERSON> and <PERSON>]{boyd2004convex}
<PERSON> and <PERSON><PERSON>.
\newblock \emph{Convex Optimization}.
\newblock Cambridge University Press, 2004.

\bibitem[<PERSON> et~al.(2018)<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>]{clark2018arc}
<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>rwal, <PERSON>issa <PERSON>hoenick, and Oyvind Tafjord.
\newblock Think you have solved question answering? try arc, the ai2 reasoning challenge.
\newblock \emph{arXiv:1803.05457}, 2018.

\bibitem[Cobbe et~al.(2021)Cobbe, Kosaraju, Bavarian, et~al.]{cobbe2021gsm8k}
Karl Cobbe, Vineet Kosaraju, Mohammad Bavarian, et~al.
\newblock Training verifiers to solve math word problems.
\newblock \emph{arXiv:2110.14168}, 2021.

\bibitem[Feng et~al.(2024)Feng, Hao, Zhang, Han, and Wang]{feng2024moa}
Wenfeng Feng, Chuzhan Hao, Yuewei Zhang, Yu~Han, and Hao Wang.
\newblock Mixture-of-loras: An efficient multitask tuning for large language models.
\newblock \emph{arXiv preprint arXiv:2403.03432}, 2024.

\bibitem[Hendrycks et~al.(2021)Hendrycks, Burns, Basart, Zou, Mazeika, Song, and Steinhardt]{hendrycks2021mmlu}
Dan Hendrycks, Collin Burns, Steven Basart, Andy Zou, Mantas Mazeika, Dawn Song, and Jacob Steinhardt.
\newblock Measuring massive multitask language understanding.
\newblock In \emph{ICLR}, 2021.

\bibitem[Ji et~al.(2023)Ji, Liu, Dai, Pan, Zhang, Bian, Chen, Sun, Wang, and Yang]{beavertails2023}
Jiaming Ji, Mickel Liu, Juntao Dai, Xuehai Pan, Chi Zhang, Ce~Bian, Boyuan Chen, Ruiyang Sun, Yizhou Wang, and Yaodong Yang.
\newblock Beavertails: Towards improved safety alignment of llm via a human-preference dataset.
\newblock \emph{NeurIPS Datasets and Benchmarks}, 2023.

\bibitem[Lin et~al.(2022)Lin, Hilton, and Evans]{lin2022truthfulqa}
Stephanie Lin, Jacob Hilton, and Owain Evans.
\newblock Truthfulqa: Measuring how models mimic human falsehoods.
\newblock In \emph{ACL}, 2022.

\bibitem[Lin et~al.(2024)Lin, Lin, Xiong, Diao, Liu, Zhang, Pan, Wang, Hu, Zhang, et~al.]{lin2024mitigating}
Yong Lin, Hangyu Lin, Wei Xiong, Shizhe Diao, Jianmeng Liu, Jipeng Zhang, Rui Pan, Haoxiang Wang, Wenbin Hu, Hanning Zhang, et~al.
\newblock Mitigating the alignment tax of rlhf.
\newblock In \emph{EMNLP}, 2024.

\bibitem[Pfeiffer et~al.(2021)Pfeiffer, Vuli{\'c}, Ruder, et~al.]{pfeiffer2020adapterfusion}
Jonas Pfeiffer, Ivan Vuli{\'c}, Sebastian Ruder, et~al.
\newblock Adapterfusion: Non-destructive task composition for transfer learning.
\newblock In \emph{EACL}, 2021.

\bibitem[Qi et~al.(2025)Qi, Lyu, Yang, Wang, Bai, and Cui]{midpo2025}
Yupeng Qi, Ziyu Lyu, Min Yang, Yanlin Wang, Lu~Bai, and Lixin Cui.
\newblock Midpo: Dual preference optimization for safety and helpfulness via a mixture-of-experts framework.
\newblock \emph{arXiv:2506.02460}, 2025.

\bibitem[Rafailov et~al.(2023)Rafailov, Sharma, Maddison, Metz, Ramesh, et~al.]{rafailov2023dpo}
Rafael Rafailov, Archit Sharma, Chris Maddison, Luke Metz, Aditya Ramesh, et~al.
\newblock Direct preference optimization: Your language model is secretly a reward model.
\newblock In \emph{NeurIPS}, 2023.
\newblock URL \url{https://arxiv.org/abs/2305.18290}.

\bibitem[Tekin et~al.(2024)Tekin, Ilhan, Huang, Hu, Yahn, and Liu]{tekin2024h3fusion}
Selim~Furkan Tekin, Fatih Ilhan, Tiansheng Huang, Sihao Hu, Zachary Yahn, and Ling Liu.
\newblock $h^3$fusion: Helpful, harmless, honest fusion of aligned llms.
\newblock \emph{arXiv preprint arXiv:2411.17792}, 2024.

\bibitem[Yu et~al.(2024)]{yu2024mole}
Weixin Yu et~al.
\newblock Mixture of lora experts (mole).
\newblock In \emph{ICLR}, 2024.
\newblock URL \url{https://www.microsoft.com/en-us/research/wp-content/uploads/2024/04/2404.13628.pdf}.

\bibitem[Zellers et~al.(2019)Zellers, Holtzman, Bisk, Farhadi, and Choi]{zellers2019hellaswag}
Rowan Zellers, Ari Holtzman, Yonatan Bisk, Ali Farhadi, and Yejin Choi.
\newblock Hellaswag: Can a machine really finish your sentence?
\newblock In \emph{ACL}, 2019.

\bibitem[Zheng et~al.(2024)]{zheng2024cdpo}
Zizhan Zheng et~al.
\newblock Constrained dpo (c-dpo): Dual gradient descent for safe preference optimization.
\newblock \emph{arXiv preprint arXiv:2403.02475}, 2024.

\bibitem[Zhou et~al.(2023)Zhou, Liu, Shao, Yue, Yang, Ouyang, and Qiao]{zhou2023modpo}
Zhanhui Zhou, Jie Liu, Jing Shao, Xiangyu Yue, Chao Yang, Wanli Ouyang, and Yu~Qiao.
\newblock Beyond one-preference-fits-all alignment: Multi-objective direct preference optimization.
\newblock \emph{arXiv preprint arXiv:2310.03708}, 2023.

\end{thebibliography}
