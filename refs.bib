
@inproceedings{raf<PERSON>ov2023dpo,
  title={Direct Preference Optimization: Your Language Model is Secretly a Reward Model},
  author={<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and others},
  booktitle={NeurIPS},
  year={2023},
  url={https://arxiv.org/abs/2305.18290}
}

@article{zhou2023modpo,
  title={Beyond One-Preference-Fits-All Alignment: Multi-Objective Direct Preference Optimization},
  author={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:2310.03708},
  year={2023}
}

@article{zheng2024cdpo,
  title={Constrained DPO (C-DPO): Dual Gradient Descent for Safe Preference Optimization},
  author={<PERSON>, <PERSON><PERSON><PERSON> and others},
  journal={arXiv preprint arXiv:2403.02475},
  year={2024}
}

@article{feng2024moa,
  title={Mixture-of-LoRAs: An Efficient Multitask Tuning for Large Language Models},
  author={<PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>},
  journal={arXiv preprint arXiv:2403.03432},
  year={2024}
}

@inproceedings{yu2024mole,
  title={Mixture of LoRA Experts (MOLE)},
  author={Yu, Weixin and others},
  booktitle={ICLR},
  year={2024},
  url={https://www.microsoft.com/en-us/research/wp-content/uploads/2024/04/2404.13628.pdf}
}

@article{tekin2024h3fusion,
  title={$H^3$Fusion: Helpful, Harmless, Honest Fusion of Aligned LLMs},
  author={Tekin, Selim Furkan and Ilhan, Fatih and Huang, Tiansheng and Hu, Sihao and Yahn, Zachary and Liu, Ling},
  journal={arXiv preprint arXiv:2411.17792},
  year={2024}
}

@inproceedings{pfeiffer2020adapterfusion,
  title={AdapterFusion: Non-Destructive Task Composition for Transfer Learning},
  author={Pfeiffer, Jonas and Vuli{\'c}, Ivan and Ruder, Sebastian and others},
  booktitle={EACL},
  year={2021}
}

@inproceedings{lin2024mitigating,
  title={Mitigating the Alignment Tax of RLHF},
  author={Lin, Yong and Lin, Hangyu and Xiong, Wei and Diao, Shizhe and Liu, Jianmeng and Zhang, Jipeng and Pan, Rui and Wang, Haoxiang and Hu, Wenbin and Zhang, Hanning and others},
  booktitle={EMNLP},
  year={2024}
}

@book{boyd2004convex,
  title={Convex Optimization},
  author={Boyd, Stephen and Vandenberghe, Lieven},
  year={2004},
  publisher={Cambridge University Press}
}

@article{beavertails2023,
  title={BeaverTails: Towards Improved Safety Alignment of LLM via a Human-Preference Dataset},
  author={Ji, Jiaming and Liu, Mickel and Dai, Juntao and Pan, Xuehai and Zhang, Chi and Bian, Ce and Chen, Boyuan and Sun, Ruiyang and Wang, Yizhou and Yang, Yaodong},
  journal={NeurIPS Datasets and Benchmarks},
  year={2023}
}

@inproceedings{lin2022truthfulqa,
  title={TruthfulQA: Measuring How Models Mimic Human Falsehoods},
  author={Lin, Stephanie and Hilton, Jacob and Evans, Owain},
  booktitle={ACL},
  year={2022}
}

@inproceedings{hendrycks2021mmlu,
  title={Measuring Massive Multitask Language Understanding},
  author={Hendrycks, Dan and Burns, Collin and Basart, Steven and Zou, Andy and Mazeika, Mantas and Song, Dawn and Steinhardt, Jacob},
  booktitle={ICLR},
  year={2021}
}

@article{clark2018arc,
  title={Think you have Solved Question Answering? Try ARC, the AI2 Reasoning Challenge},
  author={Clark, Peter and Cowhey, Isaac and Etzioni, Oren and Khot, Tushar and Sabharwal, Ashish and Schoenick, Carissa and Tafjord, Oyvind},
  journal={arXiv:1803.05457},
  year={2018}
}

@inproceedings{zellers2019hellaswag,
  title={HellaSwag: Can a Machine Really Finish Your Sentence?},
  author={Zellers, Rowan and Holtzman, Ari and Bisk, Yonatan and Farhadi, Ali and Choi, Yejin},
  booktitle={ACL},
  year={2019}
}

@article{cobbe2021gsm8k,
  title={Training Verifiers to Solve Math Word Problems},
  author={Cobbe, Karl and Kosaraju, Vineet and Bavarian, Mohammad and others},
  journal={arXiv:2110.14168},
  year={2021}
}

@article{bfpo2024,
  title={Bi-Factorial Preference Optimization: Balancing Safety and Helpfulness},
  author={Anonymous},
  journal={OpenReview},
  year={2025}
}

@article{midpo2025,
  title={MidPO: Dual Preference Optimization for Safety and Helpfulness via a Mixture-of-Experts Framework},
  author={Qi, Yupeng and Lyu, Ziyu and Yang, Min and Wang, Yanlin and Bai, Lu and Cui, Lixin},
  journal={arXiv:2506.02460},
  year={2025}
}

@article{disperse2024,
  title={Disperse-Then-Merge: Pushing the Limits of Instruction Tuning via Alignment Tax Reduction},
  author={Anonymous},
  journal={Findings of ACL},
  year={2024}
}

@article{hu2021lora,
  title={LoRA: Low-Rank Adaptation of Large Language Models},
  author={Hu, Edward J and Shen, Yelong and Wallis, Phillip and Allen-Zhu, Zeyuan and Li, Yuanzhi and Wang, Shean and Wang, Lu and Chen, Weizhu},
  journal={arXiv:2106.09685},
  year={2021}
}

@article{ouyang2022instruct,
  title={Training language models to follow instructions with human feedback},
  author={Ouyang, Long and Wu, Jeffrey and Jiang, Xu and Almeida, Diogo and Wainwright, Carroll and Mishkin, Pamela and Zhang, Chong and Agarwal, Sandhini and Slama, Katarina and Ray, Alex and others},
  journal={Advances in neural information processing systems},
  volume={35},
  pages={27730--27744},
  year={2022}
}

@article{ziegler2019fine,
  title={Fine-tuning language models from human preferences},
  author={Ziegler, Daniel M and Stiennon, Nisan and Wu, Jeffrey and Brown, Tom B and Radford, Alec and Amodei, Dario and Christiano, Paul and Irving, Geoffrey},
  journal={arXiv preprint arXiv:1909.08593},
  year={2019}
}

@article{stiennon2020learning,
  title={Learning to summarize with human feedback},
  author={Stiennon, Nisan and Ouyang, Long and Wu, Jeffrey and Ziegler, Daniel and Lowe, Ryan and Voss, Chelsea and Radford, Alec and Amodei, Dario and Christiano, Paul F},
  journal={Advances in Neural Information Processing Systems},
  volume={33},
  pages={3008--3021},
  year={2020}
}

@article{schulman2017ppo,
  title={Proximal policy optimization algorithms},
  author={Schulman, John and Wolski, Filip and Dhariwal, Prafulla and Radford, Alec and Klimov, Oleg},
  journal={arXiv preprint arXiv:1707.06347},
  year={2017}
}

@article{wortsman2022model,
  title={Model soups: averaging weights of multiple fine-tuned models improves accuracy without increasing inference time},
  author={Wortsman, Mitchell and Ilharco, Gabriel and Gadre, Samir Yitzhak and Roelofs, Rebecca and Gontijo-Lopes, Raphael and Morcos, Ari S and Namkoong, Hongseok and Farhadi, Ali and Carmon, Yair and Kornblith, Simon and others},
  journal={International Conference on Machine Learning},
  pages={23965--23998},
  year={2022}
}

@article{kornblith2019similarity,
  title={Similarity of neural network representations revisited},
  author={Kornblith, Simon and Norouzi, Mohammad and Lee, Honglak and Hinton, Geoffrey},
  journal={International Conference on Machine Learning},
  pages={3519--3529},
  year={2019}
}

@article{merity2016wikitext,
  title={Pointer sentinel mixture models},
  author={Merity, Stephen and Xiong, Caiming and Bradbury, James and Socher, Richard},
  journal={arXiv preprint arXiv:1609.07843},
  year={2016}
}

@misc{trl,
  title={TRL: Transformer Reinforcement Learning},
  author={von Werra, Leandro and Belkada, Younes and Tunstall, Lewis and Beeching, Edward and Thrush, Tristan and Lambert, Nathan and Huang, Shengyi},
  year={2020},
  url={https://github.com/huggingface/trl}
}

@misc{ultrafeedback,
  title={UltraFeedback: Boosting Language Models with High-quality Feedback},
  author={Cui, Ganqu and Yuan, Lifan and Ding, Ning and Yao, Guanming and Zhu, Wei and Ni, Yuan and Xie, Guotong and Liu, Zhiyuan and Sun, Maosong},
  year={2023},
  url={https://arxiv.org/abs/2310.01377}
}

@misc{stackexchangepaired,
  title={StackExchange Paired Dataset},
  author={Anthropic},
  year={2022},
  url={https://huggingface.co/datasets/HuggingFaceH4/stack-exchange-preferences}
}

@misc{hh_rlhf,
  title={Training a Helpful and Harmless Assistant with Reinforcement Learning from Human Feedback},
  author={Bai, Yuntao and Jones, Andy and Ndousse, Kamal and Askell, Amanda and Chen, Anna and DasSarma, Nova and Drain, Dawn and Fort, Stanislav and Ganguli, Deep and Henighan, Tom and others},
  year={2022},
  url={https://arxiv.org/abs/2204.05862}
}

@misc{oa_rm,
  title={OpenAssistant Conversations -- Democratizing Large Language Model Alignment},
  author={Köpf, Andreas and Kilcher, Yannic and von Rütte, Dimitri and Anagnostidis, Sotiris and Tam, Zhi-Rui and Stevens, Keith and Barhoum, Abdullah and Duc, Nguyen Minh and Stanley, Oliver and Nagyfi, Richárd and others},
  year={2023},
  url={https://arxiv.org/abs/2304.07327}
}

@misc{llama3,
  title={The Llama 3 Herd of Models},
  author={AI@Meta},
  year={2024},
  url={https://ai.meta.com/research/publications/the-llama-3-herd-of-models/}
}
